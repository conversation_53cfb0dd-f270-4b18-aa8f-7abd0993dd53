import React from 'react';
import { useTranslation } from 'react-i18next';
import { LessonFile } from '../../types/interfaces';

interface FileManagerProps {
  files: LessonFile[];
  onFileChange: (fileIndex: number, event: React.ChangeEvent<HTMLInputElement>) => void;
  onFileTypeChange: (fileIndex: number, type: 'video' | 'file') => void;
  onFileRemove: (fileIndex: number) => void;
  onFileAdd: () => void;
  lessonIndex: number;
}

const FileManager: React.FC<FileManagerProps> = ({
  files,
  onFileChange,
  onFileTypeChange,
  onFileRemove,
  onFileAdd,
  lessonIndex
}) => {
  const { t } = useTranslation();

  const getFileIcon = (type: string): string => {
    return type === 'video' ? '🎥' : '📄';
  };

  const getFileTypeColor = (type: string): string => {
    return type === 'video' ? 'bg-red-100 text-red-600' : 'bg-blue-100 text-blue-600';
  };

  const formatFileSize = (size: number): string => {
    if (size < 1024) return `${size} B`;
    if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)} KB`;
    if (size < 1024 * 1024 * 1024) return `${(size / (1024 * 1024)).toFixed(1)} MB`;
    return `${(size / (1024 * 1024 * 1024)).toFixed(1)} GB`;
  };

  const getFileName = (file: LessonFile): string => {
    if (typeof file.path === 'string') {
      return file.path.split('/').pop() || 'Unknown file';
    }
    if (file.path instanceof File) {
      return file.path.name;
    }
    return 'No file selected';
  };

  const getFileSize = (file: LessonFile): string => {
    if (file.path instanceof File) {
      return formatFileSize(file.path.size);
    }
    return '';
  };

  return (
    <div className="file-manager">
      <div className="flex items-center justify-between mb-4">
        <h4 className="text-lg font-semibold text-gray-800 flex items-center">
          📁 {t("Lesson Files")}
          <span className="ml-2 text-sm text-gray-500">({files.length})</span>
        </h4>
        <button
          type="button"
          onClick={onFileAdd}
          className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 flex items-center"
        >
          <span className="mr-2">+</span>
          {t("Add File")}
        </button>
      </div>

      <div className="space-y-4">
        {files.length === 0 ? (
          <div className="text-center py-12 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
            <div className="text-4xl mb-4">📁</div>
            <p className="text-gray-500 mb-2">{t("No files added yet")}</p>
            <p className="text-sm text-gray-400">{t("Click 'Add File' to upload lesson content")}</p>
          </div>
        ) : (
          files.map((file, fileIndex) => (
            <div
              key={fileIndex}
              className="file-item bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-all duration-200"
            >
              {file.id ? (
                // ملف موجود في السيرفر
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className={`w-12 h-12 rounded-full flex items-center justify-center text-xl ${getFileTypeColor(file.type)}`}>
                      {getFileIcon(file.type)}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <h5 className="font-medium text-gray-800">
                          {file.type === 'video' ? t("Video File") : t("Document File")}
                        </h5>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getFileTypeColor(file.type)}`}>
                          {file.type.toUpperCase()}
                        </span>
                      </div>
                      {file.path && typeof file.path === 'string' ? (
                        <a
                          href={`http://127.0.0.1:8000/storage/${file.path}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:text-blue-800 underline text-sm"
                        >
                          {getFileName(file)}
                        </a>
                      ) : (
                        <p className="text-sm text-gray-500">{t("No file path")}</p>
                      )}
                    </div>
                  </div>
                  <button
                    type="button"
                    onClick={() => onFileRemove(fileIndex)}
                    className="bg-red-500 hover:bg-red-600 text-white px-3 py-2 rounded-md text-sm transition-colors duration-200"
                  >
                    🗑️ {t("Delete")}
                  </button>
                </div>
              ) : (
                // ملف جديد لم يرفع بعد
                <div className="space-y-4">
                  <div className="flex items-center space-x-4">
                    <div className={`w-12 h-12 rounded-full flex items-center justify-center text-xl ${getFileTypeColor(file.type)}`}>
                      {getFileIcon(file.type)}
                    </div>
                    <div className="flex-1">
                      <h5 className="font-medium text-gray-800 mb-2">
                        {t("New")} {file.type === 'video' ? t("Video") : t("Document")}
                      </h5>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                        <div className="md:col-span-2">
                          <input
                            type="file"
                            onChange={(e) => onFileChange(fileIndex, e)}
                            className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-violet-500 focus:border-transparent text-sm"
                            accept={file.type === 'video' ? 'video/*' : '*/*'}
                          />
                          {file.path instanceof File && (
                            <div className="mt-2 text-xs text-gray-500">
                              {t("Size")}: {getFileSize(file)}
                            </div>
                          )}
                        </div>
                        <div className="flex space-x-2">
                          <select
                            value={file.type}
                            onChange={(e) => onFileTypeChange(fileIndex, e.target.value as 'video' | 'file')}
                            className="flex-1 p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-violet-500 focus:border-transparent text-sm"
                          >
                            <option value="video">{t("Video")}</option>
                            <option value="file">{t("Document")}</option>
                          </select>
                          <button
                            type="button"
                            onClick={() => onFileRemove(fileIndex)}
                            className="bg-red-500 hover:bg-red-600 text-white px-3 py-2 rounded-md text-sm transition-colors duration-200"
                          >
                            🗑️
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  {file.path instanceof File && (
                    <div className="bg-green-50 border border-green-200 rounded-md p-3">
                      <div className="flex items-center text-green-800">
                        <span className="mr-2">✅</span>
                        <span className="text-sm font-medium">
                          {t("File selected")}: {getFileName(file)}
                        </span>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          ))
        )}
      </div>

      {files.length > 0 && (
        <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
          <div className="flex items-center text-blue-800">
            <span className="mr-2">💡</span>
            <span className="text-sm">
              {t("Tip")}: {t("You can add multiple files to each lesson. Videos and documents are supported.")}
            </span>
          </div>
        </div>
      )}
    </div>
  );
};

export default FileManager;
