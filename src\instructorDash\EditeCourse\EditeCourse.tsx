import up from '../../assets-webp/UploadSimple.webp';
import img from '../../assets-webp/Image (28).webp';
import Button from '../../Ui/Button/Button';
import { useEffect, useState } from 'react';
import axios from 'axios';
import { getSecureCookie } from '../../utils/cookiesHelper';
import { showToast } from '../../utils/toast';
import { useParams } from 'react-router-dom';
import { Category, Lesson, myCourseProp, SubCategory } from '../../types/interfaces';
import { allCategories, fetchSingleCourse } from '../../services/courses';
import Label from '../../Ui/Label/Label';
import Head from './Head';
import { Trans, useTranslation } from 'react-i18next';
import axiosInstance from '../../services/axiosInstance';
import EditExam from './EditExam';
import Input from '../../Ui/Input/Input';
import './EditeCourse.css';

export default function EditCourse() {
  const { t } = useTranslation();
  const [step, setStep] = useState(1);
  const { id } = useParams<{ id: string }>();
  const [categories, setCategories] = useState<Category[]>([]);
  const [category_id, setCategory_id] = useState("");
  const [subCategory_id, setSubCategory_id] = useState("");
  const [subCategories, setSubCategories] = useState<SubCategory[]>([]);
  const handleNext = () => setStep((prev) => (prev < 4 ? prev + 1 : prev));
  const handlePrev = () => setStep((prev) => (prev > 1 ? prev - 1 : prev));
  const [titleEn, setTitleEn] = useState('');
  const [titleAr, setTitleAr] = useState('');
  const [level, setLevel] = useState('');
  const [duration, setDuration] = useState('');
  const [descriptionEn, setDescriptionEn] = useState('');
  const [descriptionAr, setDescriptionAr] = useState('');
  const [price, setPrice] = useState('');
  const [cover, setCover] = useState<File | string | null>(null);
  const [preview, setPreview] = useState<string | null>(null);
  const token = getSecureCookie("token");
  const [courseLanguage, setCourseLanguage] = useState('');
  const [course, setCourse] = useState<myCourseProp | null>(null);
  const [lessons, setLessons] = useState<Lesson[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // إنشاء درس جديد
async function createNewLesson(lesson: Omit<Lesson, 'id'>) {
  try {
    const formData = new FormData();
    formData.append("title[en]", lesson.titleEn);
    formData.append("title[ar]", lesson.titleAr);
    formData.append("description[en]", lesson.descriptionEn);
    formData.append("description[ar]", lesson.descriptionAr);
    formData.append("course_id", id || "");

    console.log("Creating lesson with data:", {
      titleEn: lesson.titleEn,
      titleAr: lesson.titleAr,
      descriptionEn: lesson.descriptionEn,
      descriptionAr: lesson.descriptionAr,
      courseId: id
    });

    const response = await axiosInstance.post(`/lessons`, formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });

    console.log("Lesson creation response:", response.data);

    // التحقق من وجود معرف الدرس في الاستجابة
    const newLessonId = response.data?.id || response.data?.data?.id || response.data?.lesson?.id;
    if (!newLessonId) {
      console.error("Server response:", response.data);
      throw new Error("Server didn't return lesson ID");
    }

    showToast("Lesson added successfully", "success");

    // رفع الملفات الجديدة فقط إذا كان لدينا معرف درس صالح
    for (const file of lesson.files) {
      if (file.path instanceof File) {
        try {
          await addFileToLesson(newLessonId, file.path, file.type);
        } catch (fileError) {
          console.error("Error uploading file:", fileError);
          showToast("Error uploading file", "warning");
        }
      }
    }

    return { ...response.data, id: newLessonId };
  } catch (error: any) {
    console.error("Error creating lesson:", error);
    const errorMessage = error.response?.data?.message || error.message || "Error creating lesson";
    showToast(errorMessage, "error");
    throw error;
  }
}

  // إضافة ملف إلى درس
async function addFileToLesson(lessonId: number | undefined, file: File, type: string) {
  if (!lessonId) {
    console.error("Lesson ID is undefined");
    showToast("Cannot add file - lesson ID is missing", 'error');
    return;
  }

  if (!file || !(file instanceof File)) {
    console.error("Invalid file provided:", file);
    showToast("Invalid file provided", 'error');
    return;
  }

  try {
    console.log(`Adding file to lesson ${lessonId}:`, {
      fileName: file.name,
      fileSize: file.size,
      fileType: type
    });

    const formData = new FormData();
    formData.append("lesson_id", lessonId.toString());
    formData.append("path", file);
    formData.append("type", type);

    const response = await axiosInstance.post(`/files`, formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });

    console.log("File upload response:", response.data);
    showToast(`${file.name} uploaded successfully!`, 'success');
    return response.data;
  } catch (error: unknown) {
    console.error("Error adding file:", error);
    const errorMessage = error instanceof Error ? error.message : "Error adding file";
    showToast(`Failed to upload ${file.name}: ${errorMessage}`, 'error');
    throw error;
  }
}

  // حذف ملف من درس
  async function deleteFileFromLesson(fileId: number) {
    try {
      await axiosInstance.delete(`/files/${fileId}`);
      showToast("File deleted successfully!", 'success');
    } catch (error) {
      console.error("Error deleting file:", error);
      showToast("Error deleting file", 'error');
      throw error;
    }
  }

  // معالجة رفع الصورة
  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setCover(file);
      setPreview(URL.createObjectURL(file));
    }
  };

  // تحديث معلومات الدورة
  async function updateCourseInfo(event: React.FormEvent<HTMLFormElement>) {
    event.preventDefault();

    try {
      const formData = new FormData();
      formData.append("_method", "PUT");
      formData.append("duration", duration);
      formData.append("level", level);
      formData.append("title[en]", titleEn);
      formData.append("title[ar]", titleAr);
      formData.append("description[en]", descriptionEn);
      formData.append("description[ar]", descriptionAr);
      formData.append("price", price);
      formData.append("category_id", category_id);
      formData.append("lang", "ar");
      
      if (cover && typeof cover !== 'string') {
        formData.append("cover", cover);
      }

      await axiosInstance.post(`/courses/${id}?_method=PUT`, formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });

      showToast(t("dashboard.Course updated"), 'success');
      setStep(step + 1);
    } catch (error: any) {
      console.error("Error updating course:", error);
      if (error.response?.data?.message) {
        showToast(error.response.data.message, 'error');
      } else {
        showToast(`Error updating course: ${error.message}`, 'error');
      }
    }
  }

  // تحديث الدروس
async function updateLessons(event?: React.FormEvent) {
  if (event) event.preventDefault();

  setIsSaving(true);

  try {
    // التحقق من صحة البيانات
    const invalidLessons = lessons.filter(lesson =>
      !lesson.titleEn.trim() || !lesson.titleAr.trim() ||
      !lesson.descriptionEn.trim() || !lesson.descriptionAr.trim()
    );

    if (invalidLessons.length > 0) {
      showToast("Please fill in all lesson fields", "error");
      setIsSaving(false);
      return;
    }

    const updatedLessonsArray = [...lessons];

    for (let i = 0; i < lessons.length; i++) {
      const lesson = lessons[i];

      if (lesson.id) {
        // تحديث درس موجود
        console.log(`Updating existing lesson ${lesson.id}`);
        const formData = new FormData();
        formData.append("_method", "PUT");
        formData.append("title[en]", lesson.titleEn);
        formData.append("title[ar]", lesson.titleAr);
        formData.append("description[en]", lesson.descriptionEn);
        formData.append("description[ar]", lesson.descriptionAr);

        await axiosInstance.post(`/lessons/${lesson.id}?_method=PUT`, formData, {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        });

        // رفع الملفات الجديدة لهذا الدرس
        for (const file of lesson.files) {
          if (file.path instanceof File && !file.id) {
            try {
              await addFileToLesson(lesson.id, file.path, file.type);
            } catch (fileError) {
              console.error("Error uploading file:", fileError);
              showToast("Error uploading file", "error");
            }
          }
        }
      } else {
        // إنشاء درس جديد
        console.log(`Creating new lesson: ${lesson.titleEn}`);
        try {
          const createdLesson = await createNewLesson(lesson);
          // تحديث الدرس في المصفوفة المحلية
          updatedLessonsArray[i] = { ...lesson, id: createdLesson.id };
        } catch (lessonError) {
          console.error("Error creating lesson:", lessonError);
          throw lessonError; // إعادة رمي الخطأ لإيقاف العملية
        }
      }
    }

    // تحديث حالة الدروس بالمعرفات الجديدة
    setLessons(updatedLessonsArray);

    showToast(t("dashboard.All lessons updated successfully!"), 'success');
    setStep(step + 1);
  } catch (error: unknown) {
    console.error("Error updating lessons:", error);
    const errorMessage = error instanceof Error ? error.message : "Error updating lessons";
    showToast(errorMessage, 'error');
  } finally {
    setIsSaving(false);
  }
}
  // معالجة تغيير بيانات الدرس
  const handleLessonChange = (
    lessonIndex: number,
    field: keyof Omit<Lesson, 'files' | 'id'>,
    value: string
  ) => {
    const updatedLessons = [...lessons];
    updatedLessons[lessonIndex][field] = value;
    setLessons(updatedLessons);
  };

  // معالجة تغيير الملف
  const handleFileChange = (
    lessonIndex: number,
    fileIndex: number,
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (file) {
      const updatedLessons = [...lessons];
      const fileType = file.type.includes("video") ? "video" : "file";
      updatedLessons[lessonIndex].files[fileIndex] = {
        path: file,
        type: fileType,
      };
      setLessons(updatedLessons);
    }
  };

  // إضافة درس جديد
  const addLesson = () => {
    setLessons([
      ...lessons,
      {
        titleAr: `الدرس ${lessons.length + 1}`,
        titleEn: `Lesson ${lessons.length + 1}`,
        descriptionAr: "وصف الدرس",
        descriptionEn: "Lesson description",
        files: [{ path: null, type: "video" }],
      },
    ]);
  };

  // إضافة ملف إلى درس
  const addFile = (lessonIndex: number) => {
    const updatedLessons = [...lessons];
    updatedLessons[lessonIndex].files.push({
      path: null,
      type: "video"
    });
    setLessons(updatedLessons);
  };

  // حذف ملف من درس
  const removeFile = async (lessonIndex: number, fileIndex: number) => {
    const fileToDelete = lessons[lessonIndex].files[fileIndex];
    
    if (fileToDelete.id) {
      try {
        await deleteFileFromLesson(fileToDelete.id);
        const updatedLessons = [...lessons];
        updatedLessons[lessonIndex].files.splice(fileIndex, 1);
        setLessons(updatedLessons);
      } catch (error) {
        console.error("Error deleting file:", error);
      }
    } else {
      const updatedLessons = [...lessons];
      updatedLessons[lessonIndex].files.splice(fileIndex, 1);
      setLessons(updatedLessons);
    }
  };

  // حذف درس
  const removeLesson = async (lessonIndex: number) => {
    const lessonToDelete = lessons[lessonIndex];
    
    if (lessonToDelete.id) {
      try {
        await axiosInstance.delete(`/lessons/${lessonToDelete.id}`);
        const updatedLessons = [...lessons];
        updatedLessons.splice(lessonIndex, 1);
        setLessons(updatedLessons);
        showToast('Lesson deleted successfully', 'success');
      } catch (error) {
        console.error("Error deleting lesson:", error);
        showToast('Error deleting lesson', 'error');
      }
    } else {
      const updatedLessons = [...lessons];
      updatedLessons.splice(lessonIndex, 1);
      setLessons(updatedLessons);
    }
  };

  // معالجة تغيير الفئة
  const handleCategoryChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedId = e.target.value;
    setCategory_id(selectedId);
    
    const selectedCategory = categories.find(cat => String(cat.id) === selectedId);
    if (selectedCategory) {
      setSubCategories(selectedCategory.sub_category || []);
      setSubCategory_id("");
    } else {
      setSubCategories([]);
    }
  };

  // جلب بيانات الدورة عند التحميل
  useEffect(() => {
    const fetchCourse = async () => {
      setIsLoading(true);
      try {
        console.log("Fetching course data for ID:", id);
        const response = await fetchSingleCourse(Number(id));
        const courseData = response.data || response;
        setCourse(response.data);

        console.log("Course data received:", courseData);

        if (courseData?.category) {
          if (courseData.category.main_category) {
            setCategory_id(String(courseData.category.main_category.id));
          }
          setSubCategory_id(String(courseData.category.id));
        }

        if (courseData) {
          setTitleEn(courseData.title?.en || '');
          setTitleAr(courseData.title?.ar || '');
          setDescriptionEn(courseData.description?.en || '');
          setDescriptionAr(courseData.description?.ar || '');
          setLevel(courseData.level || '');
          setCourseLanguage(courseData.course_language || '');
          setDuration(String(courseData.duration) || '');
          setPrice(String(courseData.price) || '');
          setCover(courseData.cover);

          if (courseData.cover) {
            setPreview(`http://127.0.0.1:8000/storage/${courseData.cover}`);
          }

          if (courseData.lessons && Array.isArray(courseData.lessons)) {
            console.log("Processing lessons:", courseData.lessons);
            const formattedLessons = courseData.lessons.map((lesson: any) => ({
              id: lesson.id,
              titleEn: lesson.title?.en || '',
              titleAr: lesson.title?.ar || '',
              descriptionEn: lesson.description?.en || '',
              descriptionAr: lesson.description?.ar || '',
              files: lesson.files?.map((file: any) => ({
                id: file.id,
                path: file.path || null,
                type: file.type || 'video'
              })) || []
            }));
            setLessons(formattedLessons);
            console.log("Formatted lessons:", formattedLessons);
          } else {
            // إذا لم تكن هناك دروس، أضف درس افتراضي
            setLessons([{
              titleAr: "الدرس الأول",
              titleEn: "Lesson 1",
              descriptionAr: "وصف الدرس الأول",
              descriptionEn: "First lesson description",
              files: []
            }]);
          }
        }
      } catch (error) {
        showToast("Error loading course", 'error');
        console.error("Error fetching course:", error);
      } finally {
        setIsLoading(false);
      }
    };

    if (id) {
      fetchCourse();
    }
  }, [id]);

  // جلب الفئات عند التحميل
  useEffect(() => {
    const fetchCategory = async () => {
      try {
        const data = await allCategories();
        setCategories(data);
      } catch (error) {
        console.error("Error fetching categories:", error);
      }
    };
    fetchCategory();
  }, []);

  // تحديث الفئات الفرعية عند تغيير الفئة الرئيسية
  useEffect(() => {
    if (categories.length > 0 && category_id) {
      const selectedCategory = categories.find(cat => String(cat.id) === category_id);
      if (selectedCategory) {
        setSubCategories(selectedCategory.sub_category || []);
      }
    }
  }, [categories, category_id]);

  if (isLoading) {
    return (
      <div className="bg-white rounded-md">
        <Head step={step}/>
        <div className="flex items-center justify-center h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-violet-600 mx-auto mb-4"></div>
            <p className="text-gray-600">{t("Loading course data...")}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-md">
      {/* head */}
      <Head step={step}/>
      <div className='p-5 flex border-b justify-between'>
        {step === 1 && (<h2 className='text-2xl font-semibold'>{t("Basic Information")}</h2>)}
        {step === 2 && (<h2 className='text-2xl font-semibold'>{t("Advance Information")}</h2>)}
        {step === 3 && (<h2 className='text-2xl font-semibold'>{t("Curriculum")}</h2>)}
        {step === 4 && (<h2 className='text-2xl font-semibold'>{t("Quiz")}</h2>)}
        <div className='flex gap-5'>
          <Button type='button' text='Save & Preview' onClick={handlePrev} textColor='text-violet-950' />
        </div>
      </div>
      
      {/* Step 1: Basic Information */}
      <form onSubmit={updateCourseInfo}>
        {step === 1 && (
          <div className='p-5'>
            <div className='flex justify-between gap-2'>
              <div className='w-1/2'>
                <Label label="Title [en]"/>
                <Input placeholder={t("TitleP[en]")} type='text' value={titleEn} onChange={(e) => setTitleEn(e.target.value)} required />
              </div>
              <div className='w-1/2'>
                <Label label="Title [ar]"/>
                <Input placeholder={t("TitleP[ar]")} type='text' value={titleAr} onChange={(e) => setTitleAr(e.target.value)} required />
              </div>
            </div>
            <div className='flex gap-2'>
              <div className='w-1/2'>
                <Label label='Category'/>
                <select
                  className="mb-5 w-full p-4 placeholder:text-base bg-White/95 rounded-md"
                  id="category"
                  value={category_id || ""}
                  onChange={handleCategoryChange}
                  required
                >
                  <option value="">{t("SelectCategory")}</option>
                  {categories.map((cat) => (
                    <option 
                      key={cat.id} 
                      value={cat.id}
                      style={String(cat.id) === String(category_id) ? {fontWeight: 'bold', color: '#8b5cf6'} : {}}
                    >
                      {t(`topCategory.${cat.name}`)}
                    </option>
                  ))}
                </select>
              </div>
              <div className='w-1/2'>
                <Label label='SubCategory'/>
                <select
                  className="mb-5 w-full p-4 placeholder:text-base bg-White/95 rounded-md"
                  id="subCategory"
                  value={subCategory_id || ""}
                  onChange={(e) => setSubCategory_id(e.target.value)}
                  required
                >
                  <option value="">{t("SelectSubCategory")}</option>
                  {subCategories.map((sub) => (
                    <option 
                      key={sub.id} 
                      value={sub.id}
                      style={String(sub.id) === String(subCategory_id) ? {fontWeight: 'bold', color: '#8b5cf6'} : {}}
                    >
                      {sub.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>
            <div className='flex gap-2'>
              <div className='w-1/2'>
                <Label label='level' />
                <select
                  className="mb-5 w-full p-4 placeholder:text-base bg-White/95 rounded-md"
                  id="level"
                  value={level}
                  onChange={(e) => setLevel(e.target.value)}
                  required
                >
                  <option value="">{t("SelectLevel")}</option>
                  <option value="beginner">{t("CoursesSection.levels.beginner")}</option>
                  <option value="intermediate">{t("CoursesSection.levels.intermediate")}</option>
                  <option value="advance">{t("CoursesSection.levels.advance")}</option>
                </select>
              </div>
              <div className='w-1/2'>
                <Label label='CourseLanguage' />
                <select
                  className="mb-5 w-full p-4 placeholder:text-base bg-White/95 rounded-md"
                  id="lang"
                  value={courseLanguage}
                  onChange={(e) => setCourseLanguage(e.target.value)}
                  required
                >
                  <option value="">{t("SelectCourseLanguage")}</option>
                  <option value="english">{t("english")}</option>
                  <option value="arabic">{t("arabic")}</option>
                </select>
              </div>
              <div className='w-1/2'>
                <Label label='Duration' />
                <input
                  className="mb-5 w-full p-4 placeholder:text-base bg-White/95 rounded-md"
                  placeholder={t("duration_placeholder")}
                  type="text"
                  id="duration"
                  value={duration}
                  onChange={(e) => setDuration(e.target.value)}
                  required
                />
              </div>
              <div className='w-1/2'>
                <Label label='CoursesSection.Price'/>
                <input
                  className="mb-5 w-full p-4 placeholder:text-base bg-White/95 rounded-md"
                  placeholder={t('CoursePrice')}
                  type="text"
                  id="price"
                  value={price}
                  onChange={(e) => setPrice(e.target.value)}
                  required
                />
              </div>
            </div>

            <div className='mt-5 flex justify-between'>
              <Button type='button' text='Cancel' textColor='border-gray border text-violet-950' />
              <Button type='submit' text='Save & Next' textColor='text-white' Bg='bg-violet-950' />
            </div>
          </div>
        )}
      </form>
      
      {/* Step 2: Advanced Information */}
      {step === 2 && (
        <div>
          <div className='p-5 border-b'>
            <Label label='Course Thumbnail'/>
            <div className='flex gap-5'>
              <div className="mb-5">
                <div
                  className="border-2 flex border-gray-300 bg-gray-200 w-48 h-48 p-4 rounded-sm text-center cursor-pointer hover:bg-gray-100"
                  onClick={() => document.getElementById("cover")?.click()}
                >
                  {preview ? (
                    <img src={preview} alt="Course cover" className="w-full h-full object-cover" />
                  ) : (
                    <div className="flex flex-col items-center justify-center w-full h-full">
                      <img src={img} alt="Default cover" className="w-16 h-16 object-cover mb-2" />
                      <span className="text-red-500 text-sm">Cover image required</span>
                    </div>
                  )}
                </div>
                <input
                  type="file"
                  id="cover"
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="hidden"
                />
              </div>
              <div>
                <p className='w-2/3 text-base text-gray-700'>
                  <Trans i18nKey="uploadThumbnailText"
                    components={{
                      bold1: <span className="text-black font-bold text-lg" />,
                      bold2: <span className="text-black font-bold text-lg" />
                    }}/>
                </p>
                <div 
                  className='flex mt-5 gap-2 text-violet-500 font-semibold bg-[#FFEEE8] w-max p-2.5 cursor-pointer'
                  onClick={() => document.getElementById("cover")?.click()}
                >
                  <p>{t(preview ? "Change image" : "Upload image")}</p>
                  <img src={up} alt="Upload" />
                </div>
                {preview && (
                  <p className="mt-2 text-green-500">
                    {t("Current cover")}
                  </p>
                )}
              </div>
            </div>
          </div>

          <div className='p-5 border-b'>
            <Label label='Description [En]'/>
            <textarea
              id="descriptionEn"
              className="mb-5 w-full h-40 p-4 placeholder:text-base bg-White/95 rounded-md"
              placeholder={t("CoursesSection.Course description in English")}
              value={descriptionEn}
              onChange={(e) => setDescriptionEn(e.target.value)}
              required
            />
            <Label label="Description [ar]" />
            <textarea
              id="descriptionAr"
              className="mb-5 w-full h-40 p-4 placeholder:text-base bg-White/95 rounded-md"
              placeholder={t("CoursesSection.Course description in Arabic")}
              value={descriptionAr}
              onChange={(e) => setDescriptionAr(e.target.value)}
              required
            />
            <div className='mt-5 flex justify-between'>
              <Button type='button' text='Back' textColor='border-gray border text-violet-950' onClick={handlePrev} />
              <Button type='button' text='Save & Next' onClick={handleNext} textColor='text-white' Bg='bg-violet-950' />
            </div>
          </div>
        </div>
      )}
      
      {/* Step 3: Curriculum */}
      {step === 3 && (
        <form onSubmit={updateLessons} className='p-5'>
          <div className='flex flex-col gap-6'>
            <div className="bg-blue-50 p-4 rounded-lg border-l-4 border-blue-500">
              <h3 className="text-lg font-semibold text-blue-800 mb-2">
                {t("Curriculum Management")}
              </h3>
              <p className="text-blue-600 text-sm">
                {t("Add, edit, or remove lessons and their files. Each lesson can contain multiple video and document files.")}
              </p>
            </div>

            {lessons.map((lesson, lessonIndex) => (
              <div key={lessonIndex} className='bg-white border border-gray-200 shadow-sm p-6 rounded-lg mb-4'>
                <div className="flex justify-between items-center mb-4">
                  <h4 className="text-lg font-semibold text-gray-800">
                    {t("Lesson")} {lessonIndex + 1}
                    {lesson.id && <span className="text-sm text-green-600 ml-2">({t("Saved")})</span>}
                  </h4>
                  <button
                    type='button'
                    onClick={() => removeLesson(lessonIndex)}
                    className='bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-md text-sm transition-colors duration-200'
                    disabled={lessons.length <= 1}
                  >
                    {t("Remove Lesson")}
                  </button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div>
                    <Label label='Title [en]'/>
                    <input
                      type="text"
                      value={lesson.titleEn}
                      onChange={(e) => handleLessonChange(lessonIndex, "titleEn", e.target.value)}
                      placeholder="Lesson title in English"
                      className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-violet-500 focus:border-transparent"
                      required
                    />
                  </div>
                  <div>
                    <Label label='Title [ar]'/>
                    <input
                      type="text"
                      value={lesson.titleAr}
                      onChange={(e) => handleLessonChange(lessonIndex, "titleAr", e.target.value)}
                      placeholder="عنوان الدرس بالعربية"
                      className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-violet-500 focus:border-transparent"
                      required
                    />
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                  <div>
                    <Label label='Description [En]'/>
                    <textarea
                      value={lesson.descriptionEn}
                      onChange={(e) => handleLessonChange(lessonIndex, "descriptionEn", e.target.value)}
                      placeholder="Lesson description in English"
                      className="w-full p-3 border border-gray-300 rounded-md h-24 focus:ring-2 focus:ring-violet-500 focus:border-transparent resize-none"
                      required
                    />
                  </div>
                  <div>
                    <Label label='Description [ar]'/>
                    <textarea
                      value={lesson.descriptionAr}
                      onChange={(e) => handleLessonChange(lessonIndex, "descriptionAr", e.target.value)}
                      placeholder="وصف الدرس بالعربية"
                      className="w-full p-3 border border-gray-300 rounded-md h-24 focus:ring-2 focus:ring-violet-500 focus:border-transparent resize-none"
                      required
                    />
                  </div>
                </div>

                {/* عرض الملفات */}
                <div className="mb-6">
                  <div className="flex items-center justify-between mb-3">
                    <label className="block text-sm font-medium text-gray-700">{t("Lesson Files")}</label>
                    <button
                      type='button'
                      onClick={() => addFile(lessonIndex)}
                      className='bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded-md text-sm transition-colors duration-200'
                    >
                      + {t("Add File")}
                    </button>
                  </div>

                  <div className="space-y-3">
                    {lesson.files.map((file, fileIndex) => (
                      <div key={fileIndex} className='bg-gray-50 p-4 rounded-lg border border-gray-200'>
                        {file.id ? (
                          // ملف موجود في السيرفر
                          <div className='flex items-center justify-between'>
                            <div className='flex items-center space-x-3'>
                              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                                file.type === 'video' ? 'bg-red-100 text-red-600' : 'bg-blue-100 text-blue-600'
                              }`}>
                                {file.type === 'video' ? '🎥' : '📄'}
                              </div>
                              <div>
                                <p className="font-medium text-gray-800">
                                  {file.type === 'video' ? t("Video File") : t("Document File")}
                                </p>
                                {file.path && typeof file.path === 'string' ? (
                                  <a
                                    href={`http://127.0.0.1:8000/storage/${file.path}`}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="text-sm text-blue-600 hover:text-blue-800 underline"
                                  >
                                    {file.path.split('/').pop()}
                                  </a>
                                ) : (
                                  <p className="text-sm text-gray-500">{t("No file path")}</p>
                                )}
                              </div>
                            </div>
                            <button
                              type='button'
                              onClick={() => removeFile(lessonIndex, fileIndex)}
                              className='bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded-md text-sm transition-colors duration-200'
                            >
                              {t("Delete")}
                            </button>
                          </div>
                        ) : (
                          // ملف جديد لم يرفع بعد
                          <div className='space-y-3'>
                            <div className="flex items-center space-x-3">
                              <input
                                type="file"
                                onChange={(e) => handleFileChange(lessonIndex, fileIndex, e)}
                                className="flex-1 p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-violet-500 focus:border-transparent"
                                accept={file.type === 'video' ? 'video/*' : '*/*'}
                              />
                              <select
                                value={file.type}
                                onChange={(e) => {
                                  const updatedLessons = [...lessons];
                                  updatedLessons[lessonIndex].files[fileIndex].type =
                                    e.target.value as "video" | "file";
                                  setLessons(updatedLessons);
                                }}
                                className="p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-violet-500 focus:border-transparent"
                              >
                                <option value="video">{t("Video")}</option>
                                <option value="file">{t("Document")}</option>
                              </select>
                              <button
                                type='button'
                                onClick={() => removeFile(lessonIndex, fileIndex)}
                                className='bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded-md text-sm transition-colors duration-200'
                              >
                                {t("Delete")}
                              </button>
                            </div>
                          </div>
                        )}
                      </div>
                    ))}

                    {lesson.files.length === 0 && (
                      <div className="text-center py-8 text-gray-500">
                        <p>{t("No files added yet")}</p>
                        <p className="text-sm">{t("Click 'Add File' to upload lesson content")}</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}

            <div className="flex justify-center mt-6">
              <button
                type='button'
                onClick={addLesson}
                className='bg-violet-500 hover:bg-violet-600 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 shadow-md hover:shadow-lg'
              >
                + {t("Add New Lesson")}
              </button>
            </div>
          </div>

          <div className='mt-8 pt-6 border-t border-gray-200 flex justify-between'>
            <Button
              type='button'
              text={t("Back")}
              textColor='border-gray-300 border text-gray-700 hover:bg-gray-50'
              onClick={handlePrev}
              disabled={isSaving}
            />
            <Button
              type='submit'
              text={isSaving ? t("Saving...") : t("Save & Next")}
              textColor='text-white'
              Bg={isSaving ? 'bg-gray-400 cursor-not-allowed' : 'bg-violet-600 hover:bg-violet-700 shadow-md hover:shadow-lg'}
              disabled={isSaving}
            />
          </div>
        </form>
      )}
      
      {/* Step 4: Publish Course */}
      {step === 4 && (
        <EditExam courseId={Number(id)} />
      )}
    </div>
  );
}