// أدوات اختبار لصفحة تعديل الدورة

import { Lesson, LessonFile } from '../../types/interfaces';

// بيانات تجريبية للاختبار
export const mockLessonData: Lesson = {
  id: 1,
  titleEn: "Introduction to Programming",
  titleAr: "مقدمة في البرمجة",
  descriptionEn: "Learn the basics of programming",
  descriptionAr: "تعلم أساسيات البرمجة",
  files: [
    {
      id: 1,
      path: "videos/lesson1.mp4",
      type: "video"
    },
    {
      id: 2,
      path: "documents/lesson1.pdf",
      type: "file"
    }
  ]
};

export const mockNewLesson: Omit<Lesson, 'id'> = {
  titleEn: "New Lesson",
  titleAr: "درس جديد",
  descriptionEn: "This is a new lesson",
  descriptionAr: "هذا درس جديد",
  files: [
    {
      path: new File(["test"], "test-video.mp4", { type: "video/mp4" }),
      type: "video"
    }
  ]
};

// دوال مساعدة للاختبار
export const validateLessonData = (lesson: Lesson): boolean => {
  return !!(
    lesson.titleEn?.trim() &&
    lesson.titleAr?.trim() &&
    lesson.descriptionEn?.trim() &&
    lesson.descriptionAr?.trim()
  );
};

export const validateFileData = (file: LessonFile): boolean => {
  return !!(
    file.path &&
    file.type &&
    (file.type === 'video' || file.type === 'file')
  );
};

// محاكاة استجابات API
export const mockApiResponses = {
  createLessonSuccess: {
    data: {
      id: 123,
      title: { en: "Test Lesson", ar: "درس تجريبي" },
      description: { en: "Test Description", ar: "وصف تجريبي" }
    }
  },
  
  createLessonError: {
    response: {
      data: {
        message: "Validation failed"
      }
    }
  },
  
  uploadFileSuccess: {
    data: {
      id: 456,
      path: "uploads/test-file.pdf",
      type: "file"
    }
  },
  
  uploadFileError: {
    response: {
      data: {
        message: "File upload failed"
      }
    }
  }
};

// دوال اختبار العمليات
export const testLessonCreation = async (
  createLessonFn: (lesson: Omit<Lesson, 'id'>) => Promise<any>,
  lessonData: Omit<Lesson, 'id'>
): Promise<boolean> => {
  try {
    const result = await createLessonFn(lessonData);
    return !!(result && result.id);
  } catch (error) {
    console.error("Lesson creation test failed:", error);
    return false;
  }
};

export const testFileUpload = async (
  uploadFileFn: (lessonId: number, file: File, type: string) => Promise<any>,
  lessonId: number,
  file: File,
  type: string
): Promise<boolean> => {
  try {
    const result = await uploadFileFn(lessonId, file, type);
    return !!(result && result.id);
  } catch (error) {
    console.error("File upload test failed:", error);
    return false;
  }
};

// دوال مساعدة للواجهة
export const simulateUserInput = (
  element: HTMLInputElement | HTMLTextAreaElement,
  value: string
): void => {
  element.value = value;
  element.dispatchEvent(new Event('change', { bubbles: true }));
};

export const simulateFileSelection = (
  fileInput: HTMLInputElement,
  file: File
): void => {
  const dataTransfer = new DataTransfer();
  dataTransfer.items.add(file);
  fileInput.files = dataTransfer.files;
  fileInput.dispatchEvent(new Event('change', { bubbles: true }));
};

// دوال التحقق من الحالة
export const checkLoadingState = (element: HTMLElement): boolean => {
  return element.classList.contains('loading') || 
         element.textContent?.includes('Loading') ||
         element.textContent?.includes('Saving') ||
         false;
};

export const checkErrorState = (element: HTMLElement): boolean => {
  return element.classList.contains('error') ||
         element.textContent?.includes('Error') ||
         false;
};

export const checkSuccessState = (element: HTMLElement): boolean => {
  return element.classList.contains('success') ||
         element.textContent?.includes('Success') ||
         element.textContent?.includes('successfully') ||
         false;
};

// دوال اختبار التحقق من صحة البيانات
export const testValidation = {
  emptyTitle: {
    titleEn: "",
    titleAr: "درس تجريبي",
    descriptionEn: "Test description",
    descriptionAr: "وصف تجريبي",
    files: []
  },
  
  emptyDescription: {
    titleEn: "Test Lesson",
    titleAr: "درس تجريبي", 
    descriptionEn: "",
    descriptionAr: "وصف تجريبي",
    files: []
  },
  
  validData: {
    titleEn: "Test Lesson",
    titleAr: "درس تجريبي",
    descriptionEn: "Test description",
    descriptionAr: "وصف تجريبي",
    files: []
  }
};

// دوال اختبار الأداء
export const measurePerformance = {
  startTimer: (): number => performance.now(),
  
  endTimer: (startTime: number): number => performance.now() - startTime,
  
  logPerformance: (operation: string, duration: number): void => {
    console.log(`${operation} took ${duration.toFixed(2)}ms`);
  }
};

// دوال اختبار الذاكرة
export const memoryTest = {
  getMemoryUsage: (): number => {
    if ('memory' in performance) {
      return (performance as any).memory.usedJSHeapSize;
    }
    return 0;
  },
  
  checkMemoryLeak: (beforeUsage: number, afterUsage: number): boolean => {
    const difference = afterUsage - beforeUsage;
    const threshold = 1024 * 1024; // 1MB
    return difference > threshold;
  }
};

// تصدير جميع الأدوات
export default {
  mockLessonData,
  mockNewLesson,
  validateLessonData,
  validateFileData,
  mockApiResponses,
  testLessonCreation,
  testFileUpload,
  simulateUserInput,
  simulateFileSelection,
  checkLoadingState,
  checkErrorState,
  checkSuccessState,
  testValidation,
  measurePerformance,
  memoryTest
};
