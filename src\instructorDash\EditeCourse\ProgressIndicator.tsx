import React from 'react';
import { useTranslation } from 'react-i18next';

interface ProgressIndicatorProps {
  currentStep: number;
  totalSteps: number;
  stepNames: string[];
  completedSteps?: number[];
}

const ProgressIndicator: React.FC<ProgressIndicatorProps> = ({
  currentStep,
  totalSteps,
  stepNames,
  completedSteps = []
}) => {
  const { t } = useTranslation();

  const getStepStatus = (stepIndex: number): 'completed' | 'active' | 'inactive' => {
    if (completedSteps.includes(stepIndex + 1)) return 'completed';
    if (stepIndex + 1 === currentStep) return 'active';
    return 'inactive';
  };

  const getStepIcon = (stepIndex: number, status: string): string => {
    if (status === 'completed') return '✓';
    return (stepIndex + 1).toString();
  };

  return (
    <div className="progress-container mb-8">
      {/* شريط التقدم */}
      <div className="relative mb-6">
        <div className="absolute top-1/2 left-0 w-full h-1 bg-gray-200 rounded-full transform -translate-y-1/2"></div>
        <div 
          className="absolute top-1/2 left-0 h-1 bg-gradient-to-r from-violet-500 to-purple-600 rounded-full transform -translate-y-1/2 transition-all duration-500"
          style={{ width: `${((currentStep - 1) / (totalSteps - 1)) * 100}%` }}
        ></div>
        
        {/* نقاط الخطوات */}
        <div className="relative flex justify-between">
          {Array.from({ length: totalSteps }, (_, index) => {
            const status = getStepStatus(index);
            return (
              <div
                key={index}
                className={`
                  flex items-center justify-center w-8 h-8 rounded-full border-2 font-semibold text-sm transition-all duration-300
                  ${status === 'completed' 
                    ? 'bg-green-500 border-green-500 text-white' 
                    : status === 'active'
                    ? 'bg-violet-500 border-violet-500 text-white shadow-lg scale-110'
                    : 'bg-white border-gray-300 text-gray-400'
                  }
                `}
              >
                {getStepIcon(index, status)}
              </div>
            );
          })}
        </div>
      </div>

      {/* أسماء الخطوات */}
      <div className="flex justify-between text-sm">
        {stepNames.map((stepName, index) => {
          const status = getStepStatus(index);
          return (
            <div
              key={index}
              className={`
                text-center transition-all duration-300
                ${status === 'active' 
                  ? 'text-violet-600 font-semibold' 
                  : status === 'completed'
                  ? 'text-green-600 font-medium'
                  : 'text-gray-400'
                }
              `}
              style={{ maxWidth: `${100 / totalSteps}%` }}
            >
              {t(stepName)}
            </div>
          );
        })}
      </div>

      {/* معلومات إضافية */}
      <div className="mt-4 text-center">
        <p className="text-sm text-gray-600">
          {t("Step")} {currentStep} {t("of")} {totalSteps}: {t(stepNames[currentStep - 1])}
        </p>
        <div className="mt-2">
          <div className="text-xs text-gray-500">
            {Math.round(((currentStep - 1) / (totalSteps - 1)) * 100)}% {t("Complete")}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProgressIndicator;
